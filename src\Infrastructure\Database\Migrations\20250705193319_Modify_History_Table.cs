﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Infrastructure.Database.Migrations;

/// <inheritdoc />
public partial class Modify_History_Table : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "is_deleted",
            schema: "public",
            table: "histories");

        migrationBuilder.DropColumn(
            name: "last_modified_at",
            schema: "public",
            table: "histories");

        migrationBuilder.AlterColumn<int>(
            name: "id",
            schema: "public",
            table: "histories",
            type: "integer",
            nullable: false,
            oldClrType: typeof(Guid),
            oldType: "uuid")
            .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<Guid>(
            name: "id",
            schema: "public",
            table: "histories",
            type: "uuid",
            nullable: false,
            oldClrType: typeof(int),
            oldType: "integer")
            .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

        migrationBuilder.AddColumn<bool>(
            name: "is_deleted",
            schema: "public",
            table: "histories",
            type: "boolean",
            nullable: false,
            defaultValue: false);

        migrationBuilder.AddColumn<DateTime>(
            name: "last_modified_at",
            schema: "public",
            table: "histories",
            type: "timestamp with time zone",
            nullable: true);
    }
}
